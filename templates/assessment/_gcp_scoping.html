<div id="gcpb" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate GCP-to-AWS Migration Scoping report using

				<span class="text">GCP Billing export</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'gcpb_req')"
						id="defaultOpen">Requirements</button>
					<div id="gcpb_req" class="tabcontent">
						<p>
							1. <span class="text" style="color: black; font-weight: bold">GCP Billing Export
								csv</span> from the GCP account.

							</span>
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'gcpb_steps')">Steps</button>
					<div id="gcpb_steps" class="tabcontent">
						<p style="line-height:1.6;">
							1. Go to Google Cloud Console → Billing → Reports.<br>
							2. Select the Billing Account you want.<br>
							3. Apply the desired date range and filters.<br>
							4. Click Download CSV to export the report.<br>
							5. Click the Upload button below.<br>
							</ol>
					</div>
					<button class="tablinks" onclick="openCity(event, 'gcpb_out')">Output</button>
					<div id="gcpb_out" class="tabcontent">
						<a href="https://docs.google.com/spreadsheets/d/1m1nwLpg6ov3-FYaWnQEmnmHJWwY1QV1fNvaUDCXTUMU/view?usp=sharing"
							target="_blank">GCP-to-AWS-migration-scoping-with AWS-Bill report</a>
						<div class="img-div">
							<img src="./static/assets/images/gcp-aws-scoping.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'gcpb_vid')">Video</button>
					<div id="gcpb_vid" class="tabcontent">
						Comming Soon...
						<!-- <iframe
							src="https://drive.google.com/file/d/16G5KyXKi2XYkAJ_E4H8JeXEaO8PfeIJG/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe> -->
					</div>
					<button class="tablinks" onclick="openCity(event, 'gcpb_wn')">What Next?</button>
					<div id="gcpb_wn" class="tabcontent">
						<p>
							1. Share the report using Share button on top with the right stakeholders giving
							them Viewer access.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<div>
							<form id="form-pink4" action="gcp_aws_scoping" method="post"
								enctype="multipart/form-data"
								onsubmit="addUUID(this);  revealText(this, 'pink4','');">
								<div class="form-group" style="padding-right:0px;margin-top: 10px;">
									<input type="file" id="gcp-bill-excel" name="gcp-bill-excel" accept=".CSV"
										required><br><br><br>
									<button id="cmdl" type="submit" class="btn arrow-btn tilt-btn"
										style="border-radius:5px; margin-top: -15px; margin-left: -2px;">Upload</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>


	</div>
</div>
