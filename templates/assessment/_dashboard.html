<div id="mansd" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate Assessment Dashboard on Looker Studio
				<span class="text"> for your GCP project</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'mansd_req')"
						id="defaultOpen">Requirements</button>
					<div id="mansd_req" class="tabcontent">
						<p>
							1. A filled <span class="text" style="color: black; font-weight: bold">VM
								mapping sheet</span> for the project with move groups assigned.

							</span>
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'mansd_steps')">Steps</button>
					<div id="mansd_steps" class="tabcontent">

						<p style="line-height:1.6;">
							1. Go to <a href="https://lookerstudio.google.com/navigation/datasources"
								target="_blank">https://lookerstudio.google.com/navigation/datasources</a>
							and click <span class="text"
								style="color: black; font-weight: bold">Create</span> button present on left
							top and click <span class="text" style="color: black; font-weight: bold">Data
								Source</span> and click <span class="text"
								style="color: black; font-weight: bold">Google Sheets</span>. Provide URL of
							your VM mapping sheet and select <span class="text"
								style="color: black; font-weight: bold">VMs</span> and <span class="text"
								style="color: black; font-weight: bold">Apps</span> worksheet one by
							one.<br>
							2. You will end up creating two data sources for <span class="text"
								style="color: black; font-weight: bold">VMs</span> and <span class="text"
								style="color: black; font-weight: bold">APPs</span>.<br>
							3. Go to Application <a
								href="https://lookerstudio.google.com/reporting/9da7effe-7926-4b42-a441-7b9d3d149395/page/L2qSD"
								target="_blank">Assessment report</a>. Click on <span class="text"
								style="color: black; font-weight: bold">Three dots</span> on top bar and
							click <span class="text" style="color: black; font-weight: bold">create a
								copy</span>.<br>
							4. Select the new data sources and click <span class="text"
								style="color: black; font-weight: bold">Copy Report</span> button.
							</ol>



					</div>
					<button class="tablinks" onclick="openCity(event, 'mansd_out')">Output</button>
					<div id="mansd_out" class="tabcontent">
						<p> A new Assessment report for the project.
						</p>
						<div class="img-div">
							<img src="./static/assets/images/assd.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'mansd_vid')">Video</button>
					<div id="mansd_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/1Jz4m2Jf7WnD-PkvD1tszSQIWxotnt8Cc/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'mansd_wn')">What Next?</button>
					<div id="mansd_wn" class="tabcontent">
						<p>
							1. Share the report using Share button on top with the right stakeholders giving
							them Viewer access.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<div>
							<div class="form-group" style="padding-right:0px;margin-top: 10px;">
								<br><br><br>
								&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
								&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<button id="manss"
									onclick="redirectToLink1()" class="btn arrow-btn tilt-btn"
									style="border-radius:5px;margin-left:-212px; margin-top: -25px;margin-bottom: 15px; text-transform:none;">Assessment
									Report</button>
							</div>


						</div>
					</div>
				</div>
			</div>
		</div>


	</div>


</div>