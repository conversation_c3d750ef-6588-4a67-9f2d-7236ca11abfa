<div id="mans" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate Manual Assessment Report
				<span class="text"> for your project</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'mans_req')"
						id="defaultOpen">Requirements</button>
					<div id="mans_req" class="tabcontent">
						<p>
							1. Create a copy of <span class="text"
								style="color: black; font-weight: bold">VM mapping sheet</span>. <br>
							2. Get <span class="text" style="color: black; font-weight: bold">as-is
								inventory</span>, <span class="text"
								style="color: black; font-weight: bold">target sizing</span> and <span
								class="text" style="color: black; font-weight: bold">treatment</span> from
							tool based assessment.
							</span>
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'mans_steps')">Steps</button>
					<div id="mans_steps" class="tabcontent">

						<p>1. Fill <span class="text" style="color: black; font-weight: bold">Dropdown
								sheet</span> for Technological landscape for <span class="text"
								style="color: black; font-weight: bold">web, app</span> and <span
								class="text" style="color: black; font-weight: bold">DB layer</span>.<br>
							2. Fill <span class="text" style="color: black; font-weight: bold">Dropdown
								sheet</span> for <span class="text"
								style="color: black; font-weight: bold">File servers, environments</span>
							and <span class="text" style="color: black; font-weight: bold">BU(App
								groups)</span> information.<br>
							3. Fill <span class="text" style="color: black; font-weight: bold">Dropdown
								sheet</span> for target mapping as per environment with resource details
							like <span class="text" style="color: black; font-weight: bold">VPC, subnet,
								service account, project, region</span> etc. <br>
							4. Fill <span class="text" style="color: black; font-weight: bold">Apps
								sheet</span> and add <span class="text"
								style="color: black; font-weight: bold">applications</span> with relevant
							metadata.<br>
							5. Fill <span class="text" style="color: black; font-weight: bold">VMs
								sheet</span> with metadata like <span class="text"
								style="color: black; font-weight: bold">application, environment,
								layer</span> and <span class="text"
								style="color: black; font-weight: bold">target IP addresses</span>.<br>
							6. Fill <span class="text" style="color: black; font-weight: bold">VM
								sheet</span> with <span class="text"
								style="color: black; font-weight: bold">movegroup information</span> based
							upon BU/App group, environment and dependency information. <br>
							7. Change <span class="text" style="color: black; font-weight: bold">Vm target
								data formulas</span> based upon requirements e.g. application specific
							service account rather than one per project.
						</p>


					</div>
					<button class="tablinks" onclick="openCity(event, 'mans_out')">Output</button>
					<div id="mans_out" class="tabcontent">
						<p>A new VM mapping sheet specific to the project which can be leveraged for
							Migration.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'mans_vid')">Video</button>
					<div id="mans_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/1YtPiCPxIqSGIoSaMWD7AdNaHkNDRdnGV/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'mans_wn')">What Next?</button>
					<div id="mans_wn" class="tabcontent">
						<p>
							1. Download the VM mapping sheet which has been created for the project and
							create <span class="text" style="color: black; font-weight: bold">movegroup
								sheet</span> for <span class="text"
								style="color: black; font-weight: bold">VM migration</span>.<br>
							2. For <span class="text" style="color: black; font-weight: bold">visual
								dashboards</span> of the assessment, create a copy of new report pointing to
							two new data sources with the current VM mapping sheet path and selecting sheet
							as VMs and Apps respectively.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<div>
							<div class="form-group" style="padding-right:0px;margin-top: 10px;">
								<br><br><br>
								&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
								&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<button id="manss"
									onclick="redirectToLink()" class="btn arrow-btn tilt-btn"
									style="border-radius:5px;margin-left:-212px; margin-top: -25px;margin-bottom: 15px; text-transform:none;">
									Downlod<br>VM Mapping Sheet</button>
							</div>


						</div>
					</div>
				</div>
			</div>
		</div>


	</div>
</div>