<div id="vmware" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate VMware Assessment sheet from RVTools export file
				<span class="text"> for migrating VMWare virtual machines</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'vmware_req')"
						id="defaultOpen">Requirements</button>
					<div id="vmware_req" class="tabcontent">
						<p>
							1. Require <span class="text" style="color:black; font-weight: bold">RVTool
								export sheet</span> generated for a VMWare Assessment.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'vmware_steps')">Steps</button>
					<div id="vmware_steps" class="tabcontent">

						<p>1. Upload the RVtool export sheet.<br>
							2. After uploading VMWare assessment sheet will be downloaded.<br>
						</p>

					</div>
					<button class="tablinks" onclick="openCity(event, 'vmware_out')">Output</button>
					<div id="vmware_out" class="tabcontent">
						<a href="https://docs.google.com/spreadsheets/d/1EAI3EExor9cpz97US2bNspNF-7O9HeiLi_uTqS1hOAw/view?usp=sharing"
							target="_blank">Sample Output</a>
						<!-- <div class="img-div">
											<img src="./static/assets/images/gcloudcmd.png" style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
										</div> -->

					</div>
					<button class="tablinks" onclick="openCity(event, 'vmware_vid')">Video</button>
					<div id="vmware_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/1ftHW0YbZC4ci1ddtBxye8rfv4nMw5FkQ/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'vmware_wn')">What Next?</button>
					<div id="vmware_wn" class="tabcontent">
						<p>No further action is required.</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px;  float: left; width : 100%; height : 500px;">
						<form id="form-pink3" action="vmware_m2vm" method="post"
							enctype="multipart/form-data"
							onsubmit="addUUID(this);  revealText(this, 'pink3','');">
							<div class="form-group"
								style="padding-right:0px;margin-top: 10px; margin-left: -20px;">
								<input type="file" id="excel-file" name="excel-file" accept=".xlsx"
									required><br><br><br>
								<button id="cmdl" type="submit" class="btn arrow-btn tilt-btn"
									style="border-radius:5px; margin-top: -15px; margin-left: -2px;">Upload</button>
							</div>
						</form>
						<div id="text-block-container-pink3" style="filter:none"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>