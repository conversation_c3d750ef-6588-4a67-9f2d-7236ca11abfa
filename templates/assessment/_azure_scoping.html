{% if provider == 'gcp' %}
<!-- For GCP -->
<div id="azureb" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate Azure-to-GCP Migration Scoping report using
				<span class="text">Azure Billing export</span>
			</h3>
			<div>
				<!-- Tab navigation buttons -->
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'azureb_req')"
						id="defaultAzureOpen">Requirements</button>
					<div id="azureb_req" class="tabcontent">
						<p>
							1. <span class="text" style="color: black; font-weight: bold">Azure Billing
								Export (.xlsx)</span> from the Azure portal.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'azureb_steps')">Steps</button>
					<div id="azureb_steps" class="tabcontent">
						<p style="line-height:1.6;">
							1. Follow the steps outlined in the <a
								href="https://docs.google.com/document/d/1TntF8GjwTPTIe7KLew3ut0IiCtPQNBmbffloyRgiaBo/view"
								target="_blank">Instructions</a> or <a
								href="https://storage.googleapis.com/hypatia-public-artifacts/pdfs/SOP%20_%20Export%20AWS%20Bill.pdf"
								target="_blank">instructions.pdf</a> to generate and export your Azure billing
							report.<br>
							2. Click the Upload button to select and submit your file.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'azureb_out')">Output</button>
					<div id="azureb_out" class="tabcontent">
						<a href="https://docs.google.com/spreadsheets/d/1R76c61_-sKAgMiE14EaAayoPdpdHPFbb-xYOcsigVOY/view?usp=sharing"
							target="_blank">Azure-to-GCP-migration-scoping-with-Azure-Bill report</a>
						<div class="img-div">
							<img src="./static/assets/images/Azure-GCP-scoping.png"
								style="margin-left: 0%; margin-top: -8%; width: 100%; height: auto;">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'azureb_vid')">Video</button>
					<div id="azureb_vid" class="tabcontent">
						Coming Soon..
						<!-- Placeholder for a video tutorial -->
						<!-- <iframe src="https://drive.google.com/file/d/your-azure-video-id/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe> -->
					</div>
					<button class="tablinks" onclick="openCity(event, 'azureb_wn')">What Next?</button>
					<div id="azureb_wn" class="tabcontent">
						<p>
							1. Share the generated report using the Share button with the right
							stakeholders, giving them Viewer access.
						</p>
					</div>
				</div>
				<!-- Form for uploading the Azure bill file -->
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<div>
							<form id="form-blue5" action="azure_export" method="post"
								enctype="multipart/form-data"
								onsubmit="addUUID(this); revealText(this, 'blue5','');">
								<div class="form-group" style="padding-right:0px;margin-top: 10px;">
									<!-- File input specifically for Excel files -->
									<input type="file" id="azure-bill-csv" name="azure-bill-csv"
										accept=".csv" required><br><br><br>
									<button id="cmd2" type="submit" class="btn arrow-btn tilt-btn"
										style="border-radius:5px; margin-top: -15px; margin-left: -2px;">Upload</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
{% else %}
<!-- for AWS -->
<div id="azureaws" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate Azure-to-AWS Migration Scoping report using
				<span class="text-purple">Azure Billing export</span>
			</h3>
			<div>
				<!-- Tab navigation buttons -->
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'azureaws_req')"
						id="defaultAzureOpen">Requirements</button>
					<div id="azureaws_req" class="tabcontent">
						<p>
							1. <span class="text" style="color: black; font-weight: bold">Azure Billing
								Export (.xlsx)</span> from the Azure portal.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'azureaws_steps')">Steps</button>
					<div id="azureaws_steps" class="tabcontent">
						<p style="line-height:1.6;">
							1. Follow the steps outlined in the <a
								href="https://docs.google.com/document/d/1TntF8GjwTPTIe7KLew3ut0IiCtPQNBmbffloyRgiaBo/view"
								target="_blank">Instructions</a> to generate and export your Azure billing
							report.<br>
							2. Click the Upload button to select and submit your file.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'azureaws_out')">Output</button>
					<div id="azureaws_out" class="tabcontent">
						<a href="https://docs.google.com/spreadsheets/d/1xef3KBqw4_i566wveOxQXjqSlw1CFwG1X2SsHRCsHMc/view?usp=sharing"
							target="_blank">Azure-to-AWS-migration-scoping-with-Azure-Bill report</a>
						<div class="img-div">
							<img src="./static/assets/images/azure-aws-scoping.png"
								style="margin-left: 0%; margin-top: -8%; width: 100%; height: auto;">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'azureaws_vid')">Video</button>
					<div id="azureaws_vid" class="tabcontent">
						Coming Soon.. ..
						<!-- Placeholder for a video tutorial -->
						<!-- <iframe src="https://drive.google.com/file/d/your-azure-video-id/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe> -->
					</div>
					<button class="tablinks" onclick="openCity(event, 'azureaws_wn')">What Next?</button>
					<div id="azureaws_wn" class="tabcontent">
						<p>
							1. Share the generated report using the Share button with the right
							stakeholders, giving them Viewer access.
						</p>
					</div>
				</div>
				<!-- Form for uploading the Azure bill file -->
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<div>
							<form id="form-blue5" action="azure_export_aws" method="post"
								enctype="multipart/form-data"
								onsubmit="addUUID(this); revealText(this, 'blue5','');">
								<div class="form-group" style="padding-right:0px;margin-top: 10px;">
									<!-- File input specifically for Excel files -->
									<input type="file" id="azure-bill-csv" name="azure-bill-csv"
										accept=".csv" required><br><br><br>
									<button id="cmd2" type="submit" class="btn arrow-btn tilt-btn"
										style="border-radius:5px; margin-top: -15px; margin-left: -2px;">Upload</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
{% endif %}
