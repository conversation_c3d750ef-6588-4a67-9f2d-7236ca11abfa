<div id="app_assessment" {% if provider !='gcp' %}class="tab-pane fade in active show" {% else %}class="tab-pane fade" {% endif %}>
    <div class="tab-body">
        <div class="content">
            <h3>
                Generate Detailed Application Code Assessment report
                <span class="text"> for your application</span>
            </h3>
            <div>
                <div class="tab">
                    <button class="tablinks" onclick="openCity(event, 'app_req')" id="defaultOpen">Requirements</button>
                    <div id="app_req" class="tabcontent">
                        <p>
                            1. A zip file containing the application source code.
                        </p>
                    </div>
                    <button class="tablinks" onclick="openCity(event, 'app_steps')">Steps</button>
                    <div id="app_steps" class="tabcontent">
                        <p>
                            1. Upload the application source code as a zip file.<br>
                            3. Click "Assess Application" to start the assessment.<br>
                            4. The assessment report will be downloaded automatically.
                        </p>
                    </div>
                    <button class="tablinks" onclick="openCity(event, 'app_out')">Output</button>
                    <div id="app_out" class="tabcontent">
                        <a {% if provider == 'gcp' %}href=./static/assets/html/aws_to_gcp_code.html {% else %}href=./static/assets/html/gcp_to_aws_code.html {% endif %}
							target="_blank">Sample Output</a>
						<div class="img-div">
							<img {% if provider == 'gcp' %}src="./static/assets/images/aws_to_gcp.png"{% else %}src="./static/assets/images/gcp_to_aws.png"{% endif %}
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
                    </div>
                    <button class="tablinks" onclick="openCity(event, 'app_vid')">Video</button>
                    <div id="app_vid" class="tabcontent">
                        <p>Video tutorial coming soon.</p>
                    </div>
                    <button class="tablinks" onclick="openCity(event, 'app_wn')">What Next?</button>
                    <div id="app_wn" class="tabcontent">
                        <p>No further action is required.</p>
                    </div>
                </div>
                <div class="form-content">
                    <div class="card-body" style="border-radius:5px; float: left; width : 100%; height : 500px;">
                        <div>
                            <form id="form-purple1" action="application_assessment" method="post" enctype="multipart/form-data" onsubmit="addUUID(this); revealText(this, 'purple1', '');">
                                <div class="form-group" style="padding-right:0px;margin-top: 10px;">
                                    <input type="hidden" name="target_cloud" value="{{ provider }}">
                                    <span class="span-form-group">Source Code</span>
                                    <input type="file" class="form-group-class" id="zip_file" name="zip_file" accept=".zip" required><br><br>
                                    <!-- <span class="span-form-group">Target Cloud</span> -->
                                    
                                    <br><br><br><br>
                                    <button type="submit" class="btn arrow-btn tilt-btn" style="border-radius:5px; margin-top: -25px;margin-bottom: 15px;">Assess Application</button>
                                </div>
                            </form>
                            <div id="text-block-container-purple1" style="filter:none"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
