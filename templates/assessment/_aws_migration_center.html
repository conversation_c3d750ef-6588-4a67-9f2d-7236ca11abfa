<div id="aws3" {% if provider =='gcp' %}class="tab-pane fade in active show" {% else %}class="tab-pane fade" {% endif %}>
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate Google Cloud Migration Center Assessment report
				<span class="text"> for your AWS project by providing ARN</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'aws3_req')"
						id="defaultOpen">Requirements</button>
					<div id="aws3_req" class="tabcontent">
						<p>
							1. Cloud Run Google Service Account <span class="text"
								style="color: blue"><br><EMAIL></span>
							requires AWS IAM Policy <span class="text"
								style="color: black; font-weight: bold">arn:aws:iam::aws:policy/ReadOnlyAccess</span>
							on AWS project.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'aws3_steps')">Steps</button>
					<div id="aws3_steps" class="tabcontent">

						<p>1. Download <a
								href="https://drive.google.com/drive/folders/1Gi0F9cL1UVT6DzObaYdgVyGxqaKKoc8f"
								target="_blank">AWS Cloudformation file</a> and use the <a
								href="https://docs.google.com/document/d/1sblnrVuxawbzfe34IaQNSzMRvn1s1lWFB99aB3keCnI/view"
								target="_blank">instructions</a> or <a
								href="https://storage.googleapis.com/hypatia-public-artifacts/pdfs/aws-instructions-new.pdf"
								target="_blank">instructions.pdf</a>.<br>
							2. The Stack should create a IAM Role and will output its ARN. <br>
							3. You need to supply the ARN in the form.<br>
							4. Click Generate.<br>
						</p>


					</div>
					<button class="tablinks" onclick="openCity(event, 'aws3_out')">Output</button>
					<div id="aws3_out" class="tabcontent">
						<a href="https://drive.google.com/drive/folders/1GtQ6az5zVj9ndA-k0qYMmgWCiavRFMyE?usp=drive_link"
							target="_blank">Sample Output</a>
						<div class="img-div">
							<img src="./static/assets/images/aws-strata.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'aws3_vid')">Video</button>
					<div id="aws3_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/1UN9xnnOFZjzGlHxFPliI862NkyCCy6vF/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'aws3_wn')">What Next?</button>
					<div id="aws3_wn" class="tabcontent">
						<p>
							Go to Stratozone and upload the zip file.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<div>
							<form id="form-pink1" action="stratozone" method="post"
								enctype="multipart/form-data"
								onsubmit="addUUID(this);  revealText(this, 'pink1','');">
								<div class="form-group" style="padding-right:0px;margin-top: 10px;">
									<input type="hidden" id="call" name="call" value="aws">
									<span class="span-form-group">ARN</span>
									<input type="text" placeholder="Enter ARN" class="form-group-class"
										id="arn" style="border-radius:5px;" name="arn" required>
									<br><br><br><br>
									<button id="awss" name="data" type="submit"
										class="btn arrow-btn tilt-btn"
										style="border-radius:5px; margin-top: -25px;margin-bottom: 15px;">Generate</button>
								</div>
							</form>
							<div id="text-block-container-pink1" style="filter:none">
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>