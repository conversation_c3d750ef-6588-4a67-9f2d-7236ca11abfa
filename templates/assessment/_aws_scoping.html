<div id="awsb" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate AWS-to-GCP Migration Scoping report using
				<span class="text">AWS Billing export</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'awsb_req')"
						id="defaultOpen">Requirements</button>
					<div id="awsb_req" class="tabcontent">
						<p>
							1. <span class="text" style="color: black; font-weight: bold">AWS Billing Export
								pdf</span> from the aws account.

							</span>
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'awsb_steps')">Steps</button>
					<div id="awsb_steps" class="tabcontent">

						<p style="line-height:1.6;">
							1. Follow the steps outlined in the <a
								href="https://docs.google.com/document/d/1kLrqjWdPmlG3iDel4-AiO6LH0MkSBdiZTwoCj_tJD8c/view"
								target="_blank">Instructions</a> or <a
								href="https://storage.googleapis.com/hypatia-public-artifacts/pdfs/SOP%20_%20Export%20AWS%20Bill.pdf"
								target="_blank">instructions.pdf</a> to generate and export your AWS billing
							report..<br>
							2. Click on Upload button.
							</ol>
					</div>
					<button class="tablinks" onclick="openCity(event, 'awsb_out')">Output</button>
					<div id="awsb_out" class="tabcontent">
						<a href="https://docs.google.com/spreadsheets/d/1MWj8H1ceEySeEcwFAhwFgi1_cVXlW8UZ4iM4DsYuwpg"
							target="_blank">AWS-to-GCP-migration-scoping-with AWS-Bill report</a>
						<div class="img-div">
							<img src="./static/assets/images/awsb.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'awsb_vid')">Video</button>
					<div id="awsb_vid" class="tabcontent">
						<!-- Comming Soon... -->
						<iframe
							src="https://drive.google.com/file/d/16G5KyXKi2XYkAJ_E4H8JeXEaO8PfeIJG/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'awsb_wn')">What Next?</button>
					<div id="awsb_wn" class="tabcontent">
						<p>
							1. Share the report using Share button on top with the right stakeholders giving
							them Viewer access.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<div>
							<form id="form-pink4" action="aws_export" method="post"
								enctype="multipart/form-data"
								onsubmit="addUUID(this);  revealText(this, 'pink4','');">
								<div class="form-group" style="padding-right:0px;margin-top: 10px;">
									<input type="file" id="aws-bill-pdf" name="aws-bill-pdf" accept=".pdf"
										required><br><br><br>
									<button id="cmdl" type="submit" class="btn arrow-btn tilt-btn"
										style="border-radius:5px; margin-top: -15px; margin-left: -2px;">Upload</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>


	</div>
</div>