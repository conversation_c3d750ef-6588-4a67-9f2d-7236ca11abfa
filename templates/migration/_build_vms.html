<div id="ai" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate VM deployment package from build sheet
				<span class="text"> with Terraform scripts</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'ai_req')"
						id="defaultOpen">Requirements</button>
					<div id="ai_req" class="tabcontent">
						<p>
							1. Require <span class="text" style="color:black; font-weight: bold">Build
								Sheet</span> generated for a movegroup.<br>
							2. <span class="text" style="color:black; font-weight: bold">Google Cloud
								SDK</span> installed and authenticated.<br>
							3. For Terraform deployment: <span class="text"
								style="color:black; font-weight: bold">Terraform CLI</span> (v1.0+)
							installed.<br>
							4. Appropriate <span class="text" style="color:black; font-weight: bold">GCP IAM
								permissions</span> for VM creation.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'ai_steps')">Steps</button>
					<div id="ai_steps" class="tabcontent">

						<p>1. Download build sheet (<a
								href="https://docs.google.com/spreadsheets/d/1Gwts6K_O0e7hJgxb59_bO2_encJFxVZm0nG8bjyKrmI/view?usp=share_link"
								target="_blank"><u>Sample Sheet</u></a>)<br>
							2. Edit the same as required<br>
							3. Upload the build sheet.<br>
							4. A deployment package (ZIP file) will be downloaded containing:<br>
							&nbsp;&nbsp;&nbsp;&nbsp;a) <strong>gcloudcmd.sh</strong> - Shell script with
							gcloud commands<br>
							&nbsp;&nbsp;&nbsp;&nbsp;b) <strong>main.tf</strong> - Terraform configuration
							files<br>
							&nbsp;&nbsp;&nbsp;&nbsp;c) <strong>gcp_compute_engine/</strong> - Custom
							Terraform module<br>
							&nbsp;&nbsp;&nbsp;&nbsp;d) <strong>README.md</strong> - Deployment
							instructions<br>
							5. Choose your preferred deployment method:<br>
							&nbsp;&nbsp;&nbsp;&nbsp;<strong>Option A:</strong> Use gcloud commands:
							<code>chmod +x gcloudcmd.sh && ./gcloudcmd.sh</code><br>
							&nbsp;&nbsp;&nbsp;&nbsp;<strong>Option B:</strong> Use Terraform:
							<code>terraform init && terraform plan && terraform apply</code><br>
						</p>

					</div>
					<button class="tablinks" onclick="openCity(event, 'ai_out')">Output</button>
					<div id="ai_out" class="tabcontent">
						<p><strong>Package Contents:</strong><br>
							📁 <strong>vm_deployment_package.zip</strong><br>
							&nbsp;&nbsp;├── 📄 <strong>gcloudcmd.sh</strong> - Executable shell script<br>
							&nbsp;&nbsp;├── 📄 <strong>main.tf</strong> - Terraform main configuration<br>
							&nbsp;&nbsp;├── 📄 <strong>README.md</strong> - Deployment instructions<br>
							&nbsp;&nbsp;└── 📁 <strong>gcp_compute_engine/</strong> - Custom Terraform
							module</p>
						<p><a href="https://drive.google.com/file/d/1Mw5408z6n9BomcqQlpcqspXKgiu1F6ut/view?usp=sharing"
								target="_blank">Sample Deployment Package</a></p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'ai_vid')">Video</button>
					<div id="ai_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/103NBkcDm-c0C4IAv44VtnCpw0dIIazpI/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'ai_wn')">What Next?</button>
					<div id="ai_wn" class="tabcontent">
						<p>The output is a complete deployment package containing both gcloud commands and
							Terraform scripts. You have two options:</p>
						<p><strong>Option 1 - Using gcloud commands:</strong><br>
							1. Extract the ZIP file<br>
							2. Open terminal in the extracted folder<br>
							3. Run: <code>chmod +x gcloudcmd.sh && ./gcloudcmd.sh</code></p>

						<p><strong>Option 2 - Using Terraform (Recommended for Infrastructure as
								Code):</strong><br>
							1. Extract the ZIP file<br>
							2. Open terminal in the extracted folder<br>
							3. Run: <code>terraform init</code><br>
							4. Review planned changes: <code>terraform plan</code><br>
							5. Deploy VMs: <code>terraform apply</code></p>

						<p><strong>Prerequisites:</strong><br>
							- Google Cloud SDK installed and authenticated<br>
							- For Terraform: Terraform CLI installed (v1.0+)<br>
							- Appropriate GCP IAM permissions for VM creation</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px;  float: left; width : 100%; height : 500px;">
						<form id="form-pink" action="gcloudcmd" method="post" enctype="multipart/form-data"
							onsubmit="addUUID(this);  revealText(this, 'pink','');">
							<input type="hidden" id="provider" name="provider" value="{{ provider }}">
							<div class="form-group"
								style="padding-right:0px;margin-top: 10px; margin-left: -20px;">
								<input type="file" id="excel-file" name="excel-file" accept=".csv"
									required><br><br><br>
								<button id="cmdl" type="submit" class="btn arrow-btn tilt-btn"
									style="border-radius:5px; margin-top: -15px; margin-left: -2px;">Upload</button>
							</div>
						</form>
						<div id="text-block-container-pink" style="filter:none"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>