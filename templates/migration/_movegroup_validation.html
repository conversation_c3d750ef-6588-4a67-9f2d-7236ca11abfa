<div id="movegroup" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">

			<h3>
				Generate Movergroup Validation Sheet from VM Mapping sheet
				<span class="text" style="color: #009925;"> after migration</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'move_req')"
						id="defaultOpen">Requirements</button>
					<div id="move_req" class="tabcontent">
						<p>
							1. Require <span class="text" style="color:black; font-weight: bold">VM mapping
								sheet</span> filled with move groups assigned. <br>
							2. <span class="text" style="color:black; font-weight: bold">Migration</span>
							and <span class="text" style="color:black; font-weight: bold">VM build
								activity</span> done so that the validation can perform comparison of VM
							mapping sheet data against VMs present in Google Cloud for a given movegroup.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'move_steps')">Steps</button>
					<div id="move_steps" class="tabcontent">

						<p>1. Enter Movegroup name in the VM mapping sheet to be checked.<br>
							2. Make sure that all the VMs for the movegroup belong to the same project.<br>
							3. Upload latest copy of VM Mapping sheet.<br>
							4. Click upload button.<br>
							5. An Excel file will be downloaded on your computer after some time.<br>

						</p>

					</div>
					<button class="tablinks" onclick="openCity(event, 'move_out')">Output</button>
					<div id="move_out" class="tabcontent">
						<a href="https://docs.google.com/spreadsheets/d/1LYFBhooF-WVWSRjQ4Te3S_eLtOkMuWT5/view?usp=sharing&ouid=111967750052980188249&rtpof=true&sd=true"
							target="_blank">Sample Output</a>
						<div class="img-div">
							<img src="./static/assets/images/move_val.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'move_vid')">Video</button>
					<div id="move_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/1zgLV5r2PG09IyexNxpZDr_hgFI0kCKy7/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'move_wn')">What Next?</button>
					<div id="move_wn" class="tabcontent">
						<p>
							The output file will show non-compliance of the infrastructure.
							You may want to take corrective action and re-run the tool again.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<form id="form-ggreen" action="move_val" method="post" enctype="multipart/form-data"
							onsubmit="addUUID(this);  revealText(this, 'ggreen','');">
							<div class="form-group" style="padding-right:0px;margin-top: 10px;">
								<span class="span-form-group">Movegroup Name</span>
								<input type="text" placeholder="Enter Movegroup Name"
									class="form-group-class" id="move-group" style="border-radius:5px;"
									name="move-group" pattern="[a-z0-9]+" required><br><br><br>
								<span class="span-form-group">Project ID</span>
								<input type="text" placeholder="Enter the project id"
									class="form-group-class" id="projid" style="border-radius:5px;"
									name="projid" required><br><br>
								<input type="file" id="excel-file" style="border-radius:5px;"
									name="excel-file" accept=".xlsx" required><br><br><br>
								&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
								&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<button id="vall" type="submit"
									class="btn arrow-btn tilt-btn"
									style="border-radius:5px;margin-left: -216px;margin-top: -25px;margin-bottom: 15px; background-color: #009925;">Upload</button>
							</div>
						</form>
						<div id="text-block-container-ggreen" style="filter:none"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>