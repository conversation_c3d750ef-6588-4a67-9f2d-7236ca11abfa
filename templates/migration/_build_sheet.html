<div id="data" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Create build sheet csv from VM Mapping sheet
				<span class="text"> for Replatform VMs</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'data_req')"
						id="defaultOpen">Requirements</button>
					<div id="data_req" class="tabcontent">
						<p>
							1. Require <span class="text" style="color:black; font-weight: bold">VM mapping
								sheet</span> filled with <span class="text"
								style="color:black; font-weight: bold">move groups</span> assigned and
							Migration path of VM as <span class="text"
								style="color:black; font-weight: bold">Replatform</span>.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'data_steps')">Steps</button>
					<div id="data_steps" class="tabcontent">

						<p>1. Copy <a {% if provider == 'gcp' %}
								href="https://docs.google.com/spreadsheets/d/1kq2JisRRh0ePMiEloL1B5oy0dw3fFpvQYGdgwgg4Gjk/view?usp=sharing"
								{% else %}
								href="https://docs.google.com/spreadsheets/d/1op3izmi_rR7hGIOkrPNIhO04EUnfa2zklE0P0Q2MohU/edit?usp=sharing"
								{% endif %}
								target="_blank"><u>VM Mapping sheet</u></a><br>
							2. Fill it with data<br>
							3. Assign VMs based upon move groups<br>
							4. Provide Migration path of VMs as <span class="text"
								style="color:black; font-weight: bold">Replatform</span> for new build
							candidates<br>
							5. Download Excel file Locally<br>
							6. In the form, fill <br>
							&nbsp; &nbsp; a) Movegroup name<br>
							7. After uploading, you will get Movegroup group build sheet (<a
								href="https://docs.google.com/spreadsheets/d/1Gwts6K_O0e7hJgxb59_bO2_encJFxVZm0nG8bjyKrmI/view?usp=sharing"
								target="_blank"><u>Sample sheet</u></a>) is downloaded.<br>
							8. The Build sheet can be used to generate both gcloud commands and Terraform
							scripts for creating VMs with
							correct metadata.<br>
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'data_out')">Output</button>
					<div id="data_out" class="tabcontent">
						<a href="https://docs.google.com/spreadsheets/d/1Gwts6K_O0e7hJgxb59_bO2_encJFxVZm0nG8bjyKrmI/view?usp=drive_link"
							target="_blank">Sample Output</a>
						<div class="img-div">
							<img src="./static/assets/images/buildsheet.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'data_vid')">Video</button>
					<div id="data_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/1QdzYkOZNQ-5ZqgLT5J19nXdRGWQkMxbE/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'data_wn')">What Next?</button>
					<div id="data_wn" class="tabcontent">
						<p>
							Go to <span class="text" style="color: black; font-weight:bold">VM
								Commands</span> tab and upload the CSV file downloaded in this step.<br>
							You will receive a complete deployment package with both gcloud commands and
							Terraform scripts to create VMs.

						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<form id="form-green" action="buildsheet" method="post"
							enctype="multipart/form-data"
							onsubmit="addUUID(this);  revealText(this, 'green','');">
							<div class="form-group"
								style="padding-right:0px;margin-top: 10px; margin-left: -20px">
								<span class="span-form-group">Movegroup Name</span>
								<input type="hidden" id="provider" name="provider" value="{{ provider }}">
								<input type="text" placeholder="Enter Movegroup Name"
									class="form-group-class" id="move-group" style="border-radius:5px;"
									name="move-group" pattern="[a-z0-9]+" required><br><br>
								<input type="file" id="excel-file" name="excel-file" accept=".xlsx"
									required><br><br>
							</div>&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
							<button id="buildsheetl" type="submit" class="btn arrow-btn tilt-btn"
								style="border-radius:5px;margin-left: -145px;margin-top: -22px;margin-bottom: 27px;">Upload</button>
						</form>
						<div id="text-block-container-green" style="filter:none"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>