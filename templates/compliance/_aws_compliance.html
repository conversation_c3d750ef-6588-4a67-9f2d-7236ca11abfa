<!--AWS compliance-->
<div id="aws2" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate Compliance Benchmark report
				<span class="text"> for your AWS project</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'aws2_req')"
						id="defaultOpen">Requirements</button>
					<div id="aws2_req" class="tabcontent">
						<p>
							1. Cloud Run Google Service Account <span class="text"
								style="color: blue"><br><EMAIL></span>
							requires AWS IAM Policy <span class="text"
								style="color: black; font-weight: bold">arn:aws:iam::aws:policy/ReadOnlyAccess</span>
							on AWS project.
							</span><br>
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'aws2_steps')">Steps</button>
					<div id="aws2_steps" class="tabcontent">

						<p>1. Download <a
								href="https://storage.googleapis.com/hypatia-public-artifacts/hydra-aws-eks-cfn.yaml"
								target="_blank">AWS Cloudformation file</a> and use the <a
								href="https://docs.google.com/document/d/1sblnrVuxawbzfe34IaQNSzMRvn1s1lWFB99aB3keCnI/view"
								target="_blank">instructions</a> or <a
								href="https://storage.googleapis.com/hypatia-public-artifacts/pdfs/aws-instructions-new.pdf"
								target="_blank">instructions.pdf</a>.<br>
							2. The Stack should create a IAM Role and will output its ARN. <br>
							3. You need to supply the ARN in the form. <br>
							4. Need to select Benchmark Option (<a
								href="https://docs.google.com/document/d/1uWtbc3_Z339MSv7xVPDZnoK2tB5RxrpT-NuVzzUjbYc/view?usp=drive_link"
								target="_blank">options info</a>) from the dropdown list. <br>
							5. Click generate. <br>

						</p>


					</div>
					<button class="tablinks" onclick="openCity(event, 'aws2_out')">Output</button>
					<div id="aws2_out" class="tabcontent">
						<a href="https://drive.google.com/file/d/1DDAZydu5_lQYeEBilECVJEssQUvo1mVO/view?usp=drive_link"
							target="_blank">Sample Output</a>
						<div class="img-div">
							<img src="./static/assets/images/awsbench.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'aws2_vid')">Video</button>
					<div id="aws2_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/1KnhFm7LI1hL44axy4NqC3YlzsRAfqF79/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'aws2_wn')">What Next?</button>
					<div id="aws2_wn" class="tabcontent">
						<p>
							No further action is required.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<div>
							<form id="form-purple1" action="compliance" method="post"
								enctype="multipart/form-data"
								onsubmit="addUUID(this);  revealText(this, 'purple1','');">
								<div class="form-group" style="padding-right:0px;margin-top: 10px;">
									<input type="hidden" id="call" name="call" value="aws">
									<span class="span-form-group">ARN</span>
									<input type="text" placeholder="Enter ARN" class="form-group-class"
										id="arn" style="border-radius:5px;" name="arn" required><br><br><br>
									<span class="span-form-group">Benchmark Option</span>
									<select id="option" class="form-group-class"
										style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;"
										name="option">
										<option value=""
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;"
											disabled selected>Select Benchmark</option>
										<option value="benchmark.cis_v120"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											cis_v120</option>
										<option value="benchmark.cis_v130"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											cis_v130</option>
										<option value="benchmark.cis_v140"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											cis_v140</option>
										<option value="benchmark.cis_v150"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											cis_v150</option>
										<option value="benchmark.cis_controls_v8_ig1"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											cis_control_v8_ig1</option>
										<option value="benchmark.cisa_cyber_essentials"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											cisa_cyber_essentials</option>
										<option value="benchmark.audit_manager_control_tower"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											AWS Audit Manager Control Tower Guardrails</option>
										<option value="benchmark.fedramp_low_rev_4"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											fedramp_low_rev_4</option>
										<option value="benchmark.fedramp_moderate_rev_4"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											fedramp_moderate_rev_4</option>
										<option value="benchmark.ffiec"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											Federal Financial Institutions Examination Council (FFIEC)
										</option>
										<option value="benchmark.foundational_security"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											foundational_security</option>
										<option value="benchmark.gdpr"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											General Data Protection Regulation (GDPR)</option>
										<option value="benchmark.gxp_21_cfr_part_11"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											gxp_21_cfr_part_11</option>
										<option value="benchmark.gxp_eu_annex_11"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											gxp_eu_annex_11</option>
										<option value="benchmark.hipaa_final_omnibus_security_rule_2013"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											hipaa_final_omnibus_security_rule_2013</option>
										<option value="benchmark.hipaa_security_rule_2003"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											hipaa_security_rule_2003</option>
										<option value="benchmark.nist_800_171_rev_2"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											nist_800_171_rev_2</option>
										<option value="benchmark.nist_800_53_rev_4"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											nist_800_53_rev_4</option>
										<option value="benchmark.nist_800_53_rev_5"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											nist_800_53_rev_5</option>
										<option value="benchmark.nist_csf"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											nist_csf</option>
										<option value="benchmark.pci_dss_v321"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											pci_dss_v321</option>
										<option value="benchmark.rbi_cyber_security"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											rbi_cyber_security</option>
										<option value="benchmark.soc_2"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											soc_2</option>
										<option value="benchmark.other"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											other</option>
									</select><br><br><br><br>
									&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
									&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<button id="awsc" name="data"
										type="submit" class="btn arrow-btn tilt-btn"
										style="border-radius:5px;margin-left:-212px; margin-top: -25px;margin-bottom: 15px;">Generate</button>
								</div>
							</form>
							<div id="text-block-container-purple1" style="filter:none"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>