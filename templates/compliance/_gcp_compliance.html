<!--GCP comliance-->
<div id="location" class="tab-pane fade in active show">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate Compliance Benchmark report
				<span class="text"> for your GCP project</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'loca_req')">Requirements</button>
					<div id="loca_req" class="tabcontent">
						<p>
							1. Needs <span class="text" style="color: black; font-weight: bold"> Security
								Viewer </span>and<span class="text" style="color: black; font-weight: bold">
								Viewer </span> access at Organization Level for Cloud Run Service Account
							<span class="text"
								style="color: blue"><EMAIL></span><br>
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'loca_steps')">Steps</button>
					<div id="loca_steps" class="tabcontent">

						<p>1. After you have provided the access to the service account provide Project ID
							for which compliance status needs to be checked.<br>
							2. Need to select CIS v1.2, 1.3, 2.0, CFT or Forseti benchmark.<br>
							3. Click Generate.
						</p>
						<div>
							<img src="./static/assets/images/benchcom.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>

					</div>
					<button class="tablinks" onclick="openCity(event, 'loca_out')">Output</button>
					<div id="loca_out" class="tabcontent">
						<a href="https://drive.google.com/file/d/11dmrwORLmbCkyQF8IP_wlEBawExs6TLy/view?usp=sharing"
							target="_blank">Sample Output</a>
						<div class="img-div">
							<img src="./static/assets/images/gcpbench.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'loca_vid')">Video</button>
					<div id="loca_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/16NzEQx9xOwaLEYAzJUQMOF0nbvtLW1uF/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'loca_wn')">What Next?</button>
					<div id="loca_wn" class="tabcontent">
						<p>
							No further action is required.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<div>
							<form id="form-purple" action="compliance" method="post"
								enctype="multipart/form-data"
								onsubmit="addUUID(this);  revealText(this, 'purple','');">
								<div class="form-group" style="padding-right:0px;margin-top: 10px;">
									<input type="hidden" id="call" name="call" value="gcp">
									<span class="span-form-group">Project ID</span>
									<input type="text" placeholder="Enter Project ID"
										class="form-group-class" id="projid" style="border-radius:5px;"
										name="projid" required><br><br><br>
									<span class="span-form-group">Benchmark Option</span>
									<select id="option" class="form-group-class"
										style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;"
										name="option">
										<option value=""
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;"
											disabled selected>Select Benchmark</option>
										<option value="benchmark.cis_v130"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											cis_v130</option>
										<option value="benchmark.cis_v120"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											cis_v120</option>
										<option value="benchmark.cis_v200"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											cis_v200</option>
										<option value="benchmark.cft_scorecard_v1"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											cft_scorecard_v1</option>
										<option value="benchmark.forseti_security_v226"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
											forseti_security_v226</option>

									</select><br><br><br><br>
									&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
									&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<button id="gcpke" name="data"
										type="submit" class="btn arrow-btn tilt-btn"
										style="border-radius:5px;margin-left:-212px; margin-top: -25px;margin-bottom: 15px;">Generate</button>
								</div>
							</form>
							<div id="text-block-container-purple" style="filter:none"></div>
						</div>

					</div>
				</div>
			</div>
		</div>
	</div>
</div>