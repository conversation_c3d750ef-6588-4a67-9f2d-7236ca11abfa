<!--Azure compliance-->
<div id="azc" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate Compliance Benchmark report
				<span class="text"> for your Azure Account</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'azc_req')"
						id="defaultOpen">Requirements</button>
					<div id="azc_req" class="tabcontent">
						<p>
							1. To get read only access of Azure account, an <span class="text"
								style="color: black; font-weight: bold">Application</span> needs to be
							created with <span class="text" style="color: black; font-weight: bold">Reader
								Role</span> along with a <span class="text"
								style="color: black; font-weight: bold">Client Secret</span>.
							</span>
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'azc_steps')">Steps</button>
					<div id="azc_steps" class="tabcontent">

						<p>1. Follow the <a
								href="https://docs.google.com/document/u/0/d/1XvinfEBmDJprJdhCcRJgbK3Ov34Nw3Cq7S2tzxm9904/view"
								target="_blank">instructions</a> or <a
								href="https://storage.googleapis.com/hypatia-public-artifacts/pdfs/azure-instructions.pdf"
								target="_blank">instructions.pdf</a> to: <br>
							&emsp;a. Create an <span class="text"
								style="color: black; font-weight: bold">Application</span>.<br>
							&emsp;b. Attach <span class="text"
								style="color: black; font-weight: bold">Reader role</span> to it.<br>
							&emsp;c. Generate a <span class="text"
								style="color: black; font-weight: bold">Client Secret</span> for the
							application.<br>
							2. Provide credentials in the form.<br>
							3. Need to select Benchmark Option from the dropdown list.<br>
							4. Click generate.
						</p>


					</div>
					<button class="tablinks" onclick="openCity(event, 'azc_out')">Output</button>
					<div id="azc_out" class="tabcontent">
						<a href="https://drive.google.com/file/d/1NM8bvbMOrkYKN6wgPJ3tGoCEXNyd1qDG/view?usp=sharing"
							target="_blank">Sample Output</a>
						<div class="img-div">
							<img src="./static/assets/images/azcbench.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'azc_vid')">Video</button>
					<div id="azc_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/1aEYkVruMQJdLFez03EU06dzcmLOhlCmb/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'azc_wn')">What Next?</button>
					<div id="azc_wn" class="tabcontent">
						<p>
							Delete the application or secret in Azure Cloud so that it cannot be used later.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<div>
							<form id="form-purple2" action="compliance" method="post"
								enctype="multipart/form-data"
								onsubmit="addUUID(this);  revealText(this, 'purple2','');">
								<div class="form-group" style="padding-right:0px;margin-top: 10px;">
									<div class="form-group" style="padding-right:0px;margin-top: 10px;">
										<input type="hidden" id="call" name="call" value="azure">
										<span class="span-form-group">Tenant ID</span>
										<input type="text" placeholder="Enter tenant Id"
											class="form-group-class" id="tenant" style="border-radius:5px;"
											name="tenant" required><br><br><br>
										<span class="span-form-group">Application ID</span>
										<input type="text" placeholder="Enter App Id"
											class="form-group-class" id="app_id" style="border-radius:5px;"
											name="app_id" required><br><br><br>
										<span class="span-form-group">Cliet Secret</span>
										<input type="password" placeholder="Enter Client Secret"
											class="form-group-class" id="key" style="border-radius:5px;"
											name="key" required><br><br><br>
										<span class="span-form-group">Benchmark Option</span>
										<select id="option" class="form-group-class"
											style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;"
											name="option">
											<option value=""
												style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;"
												disabled selected>Select Benchmark</option>
											<option value="benchmark.cis_v130"
												style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
												cis_v130</option>
											<option value="benchmark.cis_v140"
												style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
												cis_v140</option>
											<option value="benchmark.cis_v150"
												style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
												cis_v150</option>
											<option value="benchmark.cis_v200"
												style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
												cis_v200</option>
											<option value="benchmark.hipaa_hitrust_v92"
												style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
												hipaa_hitrust_v92</option>
											<option value="benchmark.nist_sp_800_53_rev_5"
												style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
												nist_sp_800_53_rev_5</option>
											<option value="benchmark.pci_dss_v321"
												style="border-radius: 5px; border:2px solid; border-right-color: grey; border-bottom-color: grey; border-left-color: black; border-top-color: black;">
												pci_dss_v321</option>

										</select><br><br><br><br>
										&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
										&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<button id="azurc" name="data"
											type="submit" class="btn arrow-btn tilt-btn"
											style="border-radius:5px;margin-left:-212px; margin-top: -25px;margin-bottom: 15px;">Generate</button>
									</div>
							</form>
							<div id="text-block-container-purple2" style="filter:none"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>