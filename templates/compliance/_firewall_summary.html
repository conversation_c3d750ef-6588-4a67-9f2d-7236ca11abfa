<div id="work" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate Firewall rules Excel for Denial rules
				<span class="text" style="color: #FDB915">from logs json</span>
			</h3>

			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'work_req')"
						id="defaultOpen">Requirements</button>
					<div id="work_req" class="tabcontent">
						<p>
							<!-- <span class="text" style="color: red">*</span>Require access to Searce Google Group <span class="text" style="color:black; font-weight: bold">hypatia-users</span> maintained by <span class="text" style="color:black; font-weight: bold"><a href="mailto: <EMAIL>">Nakul <PERSON>rang</a></span> and <span class="text" style="color:black; font-weight: bold"><a href="mailto: <EMAIL>">Av<PERSON><PERSON></a></span>. -->
							<br> <span class="text" style="color: red">*</span>Enable logging in firewall rules.
						</p>

					</div>
					<button class="tablinks" onclick="openCity(event, 'work_steps')">Steps</button>
					<div id="work_steps" class="tabcontent">

						<p>To create a firewall denial rule summary, a json logs file is required for input. The
							excel file generated will help in diagnosing the most frequent issues first. <br>
							1. Navigate to the Logs explorer in gcp console.<br>
							2. Run the following query and navigate to actions menu to download the json
							logs.<br>
							<span
								style="background-color: #e0e0e0; color: #000; font-weight: bold; padding: 3px 6px; border-left: 4px solid #555; border-radius: 3px;">
								resource.type="sub_network" AND jsonPayload.disposition="DENIED"
							</span>


							<br>


							3. Upload the logs file as input and press the Upload button.<br>
							4. It will create the firewall rule denial summary in excel format.<br>
						</p>


					</div>
					<button class="tablinks" onclick="openCity(event, 'work_out')">Output</button>
					<div id="work_out" class="tabcontent">
						<a href="https://docs.google.com/spreadsheets/d/1Swr1q2CvdpHK9ajdImkR6mkxcBm6EvqXed-Vbkla0Qc/edit?usp=sharing"
							target="_blank">Sample Output</a>
						<div class="img-div">
							<img src="./static/assets/images/firewall_summary.png"
								style="margin-left: 0%; margin-top: -8%" width=690 height=300>
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'work_vid')">Video</button>
					<div id="work_vid" class="tabcontent">
						<p> Coming Soon...
						</p>
						<!-- <iframe src="https://drive.google.com/file/d/13l-ATZHDNFHalfnue6rerS0ah7oWMaGH/preview" width="690" height="420" allow="autoplay; fullscreen;"></iframe> -->
					</div>
					<button class="tablinks" onclick="openCity(event, 'work_wn')">What Next?</button>
					<div id="work_wn" class="tabcontent">
						<p>
							Use the Summary excel sheet to diagnose the issue.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body" style="border-radius:5px;float: left; width : 100%; height : 500px;">
						<form action="json_to_xl" method="post" enctype="multipart/form-data"
							onsubmit="addUUID(this);  revealText(this, 'yellow','');" id="form-yellow">
							<div class="form-group"
								style="padding-right:0px;margin-top: 10px; margin-left: -20px;">
								<input type="file" id="json-file" name="json-file" accept=".json"
									required><br><br><br>
								<button id="tfvarsl" type="submit" class="btn arrow-btn tilt-btn"
									style="border-radius:5px; margin-left: -2px; margin-top: -10px; margin-bottom: 35px;">Upload</button>
							</div>
						</form>
						<div id="text-block-container-yellow" style="filter:none"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>