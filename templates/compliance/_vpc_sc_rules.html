<div id="vpcsc" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate YAML configuration for VPC SC rules
				<span class="text" style="color: #FDB915">from log dumps</span>
			</h3>

			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'vpcsc_req')"
						id="defaultOpen">Requirements</button>
					<div id="vpcsc_req" class="tabcontent">
						<p>
							1. Json file containing violation logs.<br>
							2. Json file containing perimeter information.<br>
							3. Excel file having group details and ip mappings.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'vpcsc_steps')">Steps</button>
					<div id="vpcsc_steps" class="tabcontent">

						<p style="margin-bottom: 0;">
							1. Open <strong>Cloud Logging</strong> and filter for violation logs using the
							following query:
						</p>
						<div class="command-block">
							log_id("cloudaudit.googleapis.com/policy")<br>
							severity=ERROR<br>
							resource.type="audited_resource"<br>
							protoPayload.metadata."@type"=<br>"type.googleapis.com/google.cloud.audit.VpcServiceControlAuditMetadata"
						</div>
						<p style="margin-bottom: 0;">
							2. Go to <strong>Actions → Download</strong>, specify the number of logs, select
							<code>JSON</code> as the format, and click <strong>Download</strong>.<br>
							3. Download the script <a
								href="https://storage.googleapis.com/hypatia-public-artifacts/vpcsc.sh"
								target="_blank">vpcsc.sh</a> and execute it in an authenticated GCP environment
							using the following command:
						</p>
						<div class="command-block">
							<span class="cmd">sh</span> <span class="file">vpcsc.sh</span> <span
								class="arg"><organization-id></span>
						</div>
						<p>
							4. Make a copy of the <a
								href="https://docs.google.com/spreadsheets/d/1wz5ZTJ-b7D0ghk_UsGUVBfJaxsNCNEFnka5qNiuQSjo"
								target="_blank">Sample Google Sheet</a> and populate it with group member and IP
							mapping details.<br>
							5. Export the completed Google Sheet as an <code>.xlsx</code> file.<br>
							6. Upload all required files in their respective fields.
						</p>


					</div>
					<button class="tablinks" onclick="openCity(event, 'vpcsc_out')">Output</button>
					<div id="vpcsc_out" class="tabcontent">
						<a href="https://docs.google.com/spreadsheets/d/1tJs6XD3_sbqa6_rQfsLMzhuHuwzWyVC2/view?usp=drive_link&ouid=105634715941788046732&rtpof=true&sd=true"
							target="_blank">Sample Output</a>
						<div class="img-div">
							<img src="./static/assets/images/tfvars.png"
								style="margin-left: 0%; margin-top: -8%" width=auto height=auto>
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'vpcsc_vid')">Video</button>
					<div id="vpcsc_vid" class="tabcontent">
						<iframe src="https://drive.google.com/file/d/13l-ATZHDNFHalfnue6rerS0ah7oWMaGH/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'vpcsc_wn')">What Next?</button>
					<div id="vpcsc_wn" class="tabcontent">
						<p>
							Share the zip file with <span class="text" style="color:black; font-weight: bold"><a
									href="mailto: <EMAIL>">Samir Patel</a></span>
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body" style="border-radius:5px;float: left; width : 100%; height : 500px;">
						<form action="compliance" method="post" enctype="multipart/form-data"
							onsubmit="addUUID(this);  revealText(this, 'yellow1','');" id="form-yellow1">
							<div class="form-group"
								style="padding-right:0px;margin-top: 10px; margin-left: -20px;">
								<input type="hidden" id="call" name="call" value="vpcsc">
								<span class="span-form-group">Logs (Json)</span>
								<input type="file" id="logFile" name="logFile" accept=".json"
									required><br><br><br>
								<span class="span-form-group">Perimeters (Zip)</span>
								<input type="file" id="perimeterFile" name="perimeterFile" accept=".zip"
									required><br><br><br>
								<span class="span-form-group">Groups Data (Excel)</span>
								<input type="file" id="excel-file" name="excel-file" accept=".xlsx"
									required><br><br><br>
								<button id="vpc_sc" type="submit" class="btn arrow-btn tilt-btn"
									style="border-radius:5px; margin-left: -2px; margin-top: -10px; margin-bottom: 35px;">Upload</button>
							</div>
						</form>
						<div id="text-block-container-yellow1" style="filter:none"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>