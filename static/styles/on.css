  /* Styling for the revealed text block */

  .text-block-blue {
  position: fixed;
  top: 45%;
  left: 55%;
  padding-right: 30px;
  padding-left:30px;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  padding: 50px;
  border: 0.5px  #0064ff;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  max-width: 80%;
  overflow-y: auto;
  }

  .text-block-blue1 {
  position: fixed;
  top: 45%;
  left: 55%;
  padding-right: 30px;
  padding-left:30px;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  padding: 50px;
  border: 0.5px  #0064ff;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  max-width: 80%;
  overflow-y: auto;
  }


  .text-block-green {
  position: fixed;
  top: 45%;
  left: 55%;
  padding-right: 30px;
  padding-left:30px;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  padding: 50px;
  border: 0.5px #00dc96;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  max-width: 80%;
  overflow-y: auto;
  }
  .text-block-ggreen {
  position: fixed;
  top: 45%;
  left: 55%;
  padding-right: 30px;
  padding-left:30px;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  padding: 50px;
  border: 0.5px #009925;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  max-width: 80%;
  overflow-y: auto;
  }

  .text-block-pink {
  position: fixed;
  height: auto;
  width: auto;
  box-sizing: border-box;
  top: 45%;
  left: 55%;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  padding: 50px;
  border: 0.5px #ff2850;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

  overflow-y: auto;
  }

  .text-block-pink1 {
  position: fixed;
  height: auto;
  width: auto;
  box-sizing: border-box;
  top: 45%;
  left: 55%;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  padding: 50px;
  border: 0.5px #ff2850;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

  overflow-y: auto;
  }

  .text-block-pink2 {
  position: fixed;
  height: auto;
  width: auto;
  box-sizing: border-box;
  top: 45%;
  left: 55%;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  padding: 50px;
  border: 0.5px #ff2850;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

  overflow-y: auto;
  }

  .text-block-pink3 {
    position: fixed;
    height: auto;
    width: auto;
    box-sizing: border-box;
    top: 45%;
    left: 55%;
    transform: translate(-50%, -50%);
    background-color: #f2f2f2;
    padding: 50px;
    border: 0.5px #ff2850;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  
    overflow-y: auto;
    }

  .text-block-tilt {
  position: fixed;
  height: auto;
  width: auto;
  box-sizing: border-box;
  top: 45%;
  left: 55%;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  padding: 50px;
  border: 0.5px #29c8e1;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

  overflow-y: none;
  }

  .text-block-tilt1 {
  position: fixed;
  height: auto;
  width: auto;
  box-sizing: border-box;
  top: 45%;
  left: 55%;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  padding: 50px;
  border: 0.5px #29c8e1;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

  overflow-y: none;
  }

  .text-block-tilt2 {
  position: fixed;
  height: auto;
  width: auto;
  box-sizing: border-box;
  top: 45%;
  left: 55%;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  padding: 50px;
  border: 0.5px #29c8e1;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

  overflow-y: none;
  }

  .text-block-yellow {
  position: fixed;
  height: auto;
  width: auto;
  box-sizing: border-box;
  top: 45%;
  left: 55%;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  padding: 50px;
  border: 0.5px #FDB915;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

  overflow-y: none;
  }
  .text-block-yellow1 {
  position: fixed;
  height: auto;
  width: auto;
  box-sizing: border-box;
  top: 45%;
  left: 55%;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  padding: 50px;
  border: 0.5px #FDB915;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

  overflow-y: none;
  }

  .text-block-purple {
  position: fixed;
  top: 45%;
  left: 55%;
  padding-right: 30px;
  padding-left:30px;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  padding: 50px;
  border: 0.5px  #734BFF;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  max-width: 80%;
  overflow-y: auto;
  }

  .text-block-purple1 {
  position: fixed;
  top: 45%;
  left: 55%;
  padding-right: 30px;
  padding-left:30px;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  padding: 50px;
  border: 0.5px  #734BFF;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  max-width: 80%;
  overflow-y: auto;
  }

  .text-block-purple2 {
  position: fixed;
  top: 45%;
  left: 55%;
  padding-right: 30px;
  padding-left:30px;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  padding: 50px;
  border: 0.5px  #734BFF;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  max-width: 80%;
  overflow-y: auto;
  }

  .text-block-orange {
  position: fixed;
  height: auto;
  width: auto;
  box-sizing: border-box;
  top: 45%;
  left: 55%;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  padding: 50px;
  border: 0.5px #F08000;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

  overflow-y: none;
  }

  .text-block-orange1 {
  position: fixed;
  height: auto;
  width: auto;
  box-sizing: border-box;
  top: 45%;
  left: 55%;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  padding: 50px;
  border: 0.5px #F08000;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

  overflow-y: none;
  }

  .text-block-orange2 {
    position: fixed;
    height: auto;
    width: auto;
    box-sizing: border-box;
    top: 45%;
    left: 55%;
    transform: translate(-50%, -50%);
    background-color: #f2f2f2;
    padding: 50px;
    border: 0.5px #F08000;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  
    overflow-y: none;
    }

  /* Styling for the close button */
  .close-button-blue {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 30px;
  color: #0064ff;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-blue:hover {
  background-color: #0064ff;
  color: white;
  }

  .close-button-blue1 {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 30px;
  color: #0064ff;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-blue1:hover {
  background-color: #0064ff;
  color: white;
  }

  .close-button-green {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 20px;
  color: #00dc96;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-green:hover {
  background-color: #00dc96;
  color: white;
  }

  .close-button-ggreen {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 20px;
  color: #009925;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-ggreen:hover {
  background-color: #009925;
  color: white;
  }

  .close-button-pink {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 20px;
  color: #ff2850;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-pink:hover {
  background-color: #ff2850;
  color: white;
  }

  .close-button-pink1 {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 20px;
  color: #ff2850;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-pink1:hover {
  background-color: #ff2850;
  color: white;
  }

  .close-button-pink2 {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 20px;
  color: #ff2850;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-pink2:hover {
  background-color: #ff2850;
  color: white;
  }

  .close-button-pink3 {
    position: fixed;
    top: 1px;
    right: 1px;
    font-size: 20px;
    color: #ff2850;
    border: none;
    background-color: transparent;
    cursor: pointer;
    }
    .close-button-pink3:hover {
    background-color: #ff2850;
    color: white;
    }

  .close-button-tilt {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 20px;
  color: #29c8e1;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-tilt:hover {
  background-color: #29c8e1;
  color: white;
  }

  .close-button-tilt1 {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 20px;
  color: #29c8e1;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-tilt1:hover {
  background-color: #29c8e1;
  color: white;
  }

  .close-button-tilt2 {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 20px;
  color: #29c8e1;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-tilt2:hover {
  background-color: #29c8e1;
  color: white;
  }

  .close-button-yellow {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 20px;
  color: #FDB915;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-yellow:hover {
  background-color: #FDB915;
  color: white;
  }
  .close-button-yellow1 {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 20px;
  color: #FDB915;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-yellow1:hover {
  background-color: #FDB915;
  color: white;
  }

  .close-button-purple {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 30px;
  color: #734BFF;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-purple:hover {
  background-color: #734BFF;
  color: white;
  }

  .close-button-purple1 {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 30px;
  color: #734BFF;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-purple1:hover {
  background-color: #734BFF;
  color: white;
  }

  .close-button-purple2 {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 30px;
  color: #734BFF;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-purple2:hover {
  background-color: #734BFF;
  color: white;
  }

  .close-button-orange {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 20px;
  color: #F08000;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-orange:hover {
  background-color: #F08000;
  color: white;
  }

  .close-button-orange1 {
  position: fixed;
  top: 1px;
  right: 1px;
  font-size: 20px;
  color: #F08000;
  border: none;
  background-color: transparent;
  cursor: pointer;
  }
  .close-button-orange1:hover {
  background-color: #F08000;
  color: white;
  }

  .close-button-orange2 {
    position: fixed;
    top: 1px;
    right: 1px;
    font-size: 20px;
    color: #F08000;
    border: none;
    background-color: transparent;
    cursor: pointer;
    }
    .close-button-orange2:hover {
    background-color: #F08000;
    color: white;
    }

  .log-button-tilt {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  left : 50%;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #29c8e1;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-tilt:hover {
  background-color: black;
  color: white;
  }

  .log-button-tilt1 {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  left : 50%;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #29c8e1;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-tilt1:hover {
  background-color: black;
  color: white;
  }

  .log-button-tilt2 {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  left : 50%;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #29c8e1;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-tilt2:hover {
  background-color: black;
  color: white;
  }

  .log-button-yellow {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  right: 100px;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #FDB915;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-yellow:hover {
  background-color: black;
  color: white;
  }
  .log-button-yellow1 {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  right: 100px;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #FDB915;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-yellow1:hover {
  background-color: black;
  color: white;
  }


  .log-button-orange {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  right: 100px;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #F08000;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-orange:hover {
  background-color: black;
  color: white;
  }

  .log-button-orange1 {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  right: 100px;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #F08000;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-orange1:hover {
  background-color: black;
  color: white;
  }

  .log-button-orange2 {
    position: fixed;
    height: 30px;
    width: 60px;
    top: 70%;
    right: 100px;
    text-align: center;
    font-size: 14px;
    color: white;
    background-color: #F08000;
    border-radius: 10px;
    cursor: pointer;
    }
    .log-button-orange2:hover {
    background-color: black;
    color: white;
    }

  .log-button-blue1 {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  right: 100px;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #0064ff;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-blue1:hover {
  background-color: black;
  color: white;
  }

  .log-button-ggreen {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  right: 100px;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #009925;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-ggreen:hover {
  background-color: black;
  color: white;
  }

  .log-button-green {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  right: 100px;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #009925;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-green:hover {
  background-color: black;
  color: white;
  }

  .log-button-purple {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  left : 50%;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #734BFF;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-purple:hover {
  background-color: black;
  color: white;
  }

  .log-button-purple1 {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  left : 50%;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #734BFF;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-purple1:hover {
  background-color: black;
  color: white;
  }

  .log-button-purple2 {
  position: fixed;
  height: 30px;
  width: 60px;
  top: 70%;
  left : 50%;
  text-align: center;
  font-size: 14px;
  color: white;
  background-color: #734BFF;
  border-radius: 10px;
  cursor: pointer;
  }
  .log-button-purple2:hover {
  background-color: black;
  color: white;
  }

  .loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  }

  .loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  }

  .loader-blue {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #0064ff;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-blue1 {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #0064ff;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-green {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #00dc96;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-ggreen {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #009925;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-pink {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #ff2850;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }
  .loader-pink1 {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #ff2850;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-pink2 {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #ff2850;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-pink3 {
    border: 8px solid #f3f3f3;
    border-top: 8px solid #ff2850;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
    }

  .loader-tilt {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #29c8e1;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-tilt1 {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #29c8e1;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-tilt2 {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #29c8e1;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-yellow {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #FDB915;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }
  .loader-yellow1 {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #FDB915;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-purple {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #734BFF;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-purple1 {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #734BFF;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-purple2 {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #734BFF;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-orange {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #F08000;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-orange1 {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #F08000;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  }

  .loader-orange2 {
    border: 8px solid #f3f3f3;
    border-top: 8px solid #F08000;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
    }



  @keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
  }

  .timer {
  display:flex;
  font-size: 15px;
  margin-top: 10px;
  margin-left: 25.5%;
  animation: spin1 0s linear infinite;
  }



  /*Slider-Selecter*/

  .toggle-switch {
  position: relative;
  display: inline-block;
  margin-top: 10px;
  width: 90px;
  height: 35.5px;
  }

  /* Style for the checkbox */
  .toggle-switch input {
  display: none;
  }

  /* Style for the slider */
  .toggle-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #4285F4;
  transition: .4s;
  border-radius: 70px;
  }

  /* Style for the text inside the slider */
  .toggle-switch .slider:before {
  position: absolute;
  content: "";
  height: 24.5px;
  width: 24.5px;
  left: 5.5px;
  bottom: 5.5px;
  background-color: #ffffff;
  background-size: 20px 20px;
  background-repeat: no-repeat;
  background-position: 5px 5px;
  transition: .4s;
  border-radius: 50%;
  }

  /* Style for the "No" text */
  .toggle-switch .slider:after {
  position: absolute;
  content: "";
  color: white;
  display: block;
  top: 50%;
  transform: translateY(-50%);
  right: 10px;
  transition: .4s;
  }

  /* Style for the "Yes" text */
  .toggle-switch input:checked + .slider:after {
  content: "";
  right: 57px;
  color: white;
  }


  /* Style when the switch is toggled on */
  .toggle-switch input:checked + .slider {
  background-color: #FF9900;
  }

  /* Style for the slider when toggled on */
  .toggle-switch input:checked + .slider:before {
  transform: translateX(54.5px);

  }

  /*Tabs inside content container*/

  .tab {
  float: right;
  border: 0px solid #ccc;
  background-color: #fff;
  width: 70%;
  height: 100%;
  }

  /* Style the buttons inside the tab */
  .tab button {
  display: block;
  background-color: inherit;
  color: black;
  padding: 22px 16px;
  width: 100%;
  border: none;
  outline: none;
  text-align: left;
  cursor: pointer;
  transition: 0.3s;
  font-size: 17px;
  /*background-color: #f1f1f1;*/
  border-bottom: 1px solid #f1f1f1;
  }

  /* Change background color of buttons on hover */
  .tab button:hover {
  background-color: #5f6368;
  outline: none;
  }

  /* Create an active/current "tab button" class */
  .tab button.active {
  background-color: #5f6368;
  color: #fff;
  outline: none;
  }
  /* Style the tab content */
  .tabcontent {
  float: left;
  padding: 0px 12px;
  background-color : #fff;
  border-bottom: 1px solid #f1f1f1;
  width: 100%;
  padding-top: 15px;
  padding-bottom: 15px;
  /*border-left: none;*/
  /*height: 300px;*/
  }
  /* before arrow on active tab */
  .tab button::after{
  content:"\203A";
  display:inline;
  float:right;
  font-weight: bold;
  outline: none;
  }
  .tab button.active::after{
  content:"";
  display:inline;
  float:right;
  outline: none;
  font-weight: bold;
  }

  /*Button Style Toggle*/
    .choice-main {
    display: flex; /* Use flexbox to align buttons in a row */
    align-items: center;
    gap: 0px; /* Adjust space between buttons */

  }

  .choice-btn {
    display: block;
    border: 2px solid transparent;
    outline: none;
    text-align: center;
    padding: 10px 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    border-radius: 0px;
    background-color: transparent;
    color: #5f6368;
    font-weight: 500;
  }

  .choice-btn:hover:not(.active) {
    background-color: #f8f9fa;
    color: #5f6368;
  }

  .choice-btn.active {
    background-color: #f0f2f5;
    color:  #0064ff;
    border: 2px solid #f0f2f5;
    font-weight: 500;
    padding: 31px 24px;
    margin-top: -22px;
    margin-bottom: -22px;
  ;
  }
  .form-content {
  float: left;
  width : 60%;
  height : 500px;
  /* max-width: 550px !important; */ /* Disabled for new UI */
  }

  @font-face {
  font-family: 'HeilHydra';
  src: url('./static/assets/fonts/hydra.ttf') format('truetype');
  }

  .img-div {
  padding-top: 10%;
  }

  .txtout {
  width: 600px;
  height: 300px ;
  box-sizing: border-box;
  overflow-y: scroll;
  margin-bottom: 10px;
  }

  .blur{
  filter: blur(5px);
  }
  .no-blur{
  filter: blur(0px);
  }
  .span-form-group {
  position: absolute;
  margin-top: -12px;
  margin-left: 7px;
  font-size: 15px;
  font-weight: bold;
  color: black;
  background: white;
  }

  .form-group-class {
  /* width: 215px; */ /* Disabled for new UI - now controlled by modern styles */
  }

  .span-form-group-projids {
  position: absolute;
  margin-top: -12px;
  margin-left: -6px;
  font-size: 15px;
  font-weight: bold;
  color: black;
  background: white;
  }

  .form-group-class-projids {
  /* width: 210px; */ /* Disabled for new UI - now controlled by modern styles */
  /* margin-left: -13px; */ /* Disabled for new UI */
  }

  .span-form-group-orgid {
  position: absolute;
  margin-top: -13px;
  margin-left: -58px;
  font-size: 15px;
  font-weight: bold;
  color: black;
  background: white;
  }

  .form-group-class-orgid {
  /* width: 210px; */ /* Disabled for new UI - now controlled by modern styles */
  /* margin-left: -65px; */ /* Disabled for new UI */
  }

  .what-we-do .what-tab {
  margin-top: 0px !important; /* Join with header above */
  max-width: 100% !important;
  width: 100% !important;
  }

  .orgid-excel-file{
  margin-left: -15px;
  }

  .what-we-do .what-tab .tab-content.section-padding {
  padding: 40px 60px !important; /* Adjusted padding for full width */
  margin-top: 0px !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  }

  .what-we-do .what-tab .tab-pane .content h3 {
  max-width: none !important;
  }

  .what-we-do .what-tab .tab-pane .content p {
  max-width: none !important;
  }

  .site-footer .footer-right {
  border-bottom: none !important;
  padding-bottom: 0px !important;
  }

  .site-footer {
  padding: 65px 0 !important;
  }

  table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 20px;
  }
  th, td {
  text-align: left;
  padding: 10px;
  border: 1px solid #ddd;
  }
  th {
  background-color: #f2f2f2;
  font-weight: bold;
  }
  tr:nth-child(even) {
  background-color: #f2f2f2;
  }
  caption {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  }
  body {
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  }
  .grid-container {
  display: grid;
  grid-template-columns: 37% 40% 40%;
  }
  .grid-item {
  background-color: rgba(255, 255, 255, 0.8);
  text-align: center;
  }
  .hidden {
  display: none;
  }

  .icon {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 9999;
  }

  .video-row {
    display: flex;
    margin-left: -69px;
    margin-bottom: 20px;
  }

  .video-column {
    width: 30%;
    margin-left:39px;
    
  }



  .video-name {
    margin-top: 10px;
    text-align: center;
  }

  .division {
    margin-top:10px;
  }

  /* Fullscreen Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1050;
}

/* Loading Box */
.loading-box {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
    position: relative;
    width: 450px;
    /* height: 200px; */
    text-align: center;
}

.loading-box .btn-close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;  /* Ensures size */
    height: 20px;
    background-color: transparent;
    opacity: 1;  /* Fix Bootstrap hidden issue */
    font-size: 1.5rem;
    border: none;
    cursor: pointer;
    color: rgb(255, 0, 0);
}

.loading-box .btn-close::before {
    content: '✖'; /* Unicode X symbol */
    font-size: 20px;
    font-weight: bold;
    display: block;
    text-align: center;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 5px;
}

  .switch {
    position: relative;
    display: inline-block;
    width: 30px;
    height: 17px;
    margin-top: 15px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 17px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 13px;
    width: 13px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #2196F3;
}

input:checked + .slider:before {
    transform: translateX(13px);
}

/* Additional fields container */
.additional-fields {
    display: none;
    transition: all 0.3s ease;
}

.additional-fields.show {
    display: block;
}

.command-block {
  background-color: #f4f4f4;
  color: #333;
  padding: 10px;
  border-radius: 5px;
  font-family: Consolas, Monaco, monospace;
  overflow-x: auto;
  display: inline-block;
  margin: 10px 0;
  border: 1px solid #ddd;
}
.cmd {
  color: #007acc;
}
.file {
  color: #d47f00;
}
.arg {
  color: #a31515;
}

.full-width-container {
  max-width: 100% !important;
  width: 100% !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* =================================================================== */
/* == MODERN UI STYLES FOR GCP & AWS (CORRECTED & CONSOLIDATED)    == */
/* =================================================================== */

/* 1. GENERAL & FONT STYLES
----------------------------------------------------------------------*/
 .tab-body{
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    max-width: 100% !important;
    width: 100% !important;
    padding-left: 60px !important;  /* Side margin for content */
    padding-right: 60px !important;
    padding-top: 0px !important;
    margin-top: 0px !important;
    box-sizing: border-box !important;
}


/* 2. HEADER STYLES (Applies to both GCP and AWS)
----------------------------------------------------------------------*/
 .content h3 {
    font-size: 28px;
    font-weight: 600;
    color: #0d6efd; /* Primary Blue */
    margin-bottom: 30px;
    margin-top: 0px;
    padding-top: 0px;
}

 .content h3 .text {
    display: block;
    font-size: 18px;
    color: #495057 !important; /* Overrides inline style */
    font-weight: 400;
    margin-top: 5px;
}


/* 3. CORE TWO-COLUMN LAYOUT (Applies to both GCP and AWS)
----------------------------------------------------------------------*/
 .content > div {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    gap: 60px !important; /* Spacing between columns */
    order: 1 !important;
    align-items: flex-start !important;
    max-width: 1200px !important; /* Fixed max width for consistency */
    width: 100% !important;
    margin: 0 auto !important; /* Center the entire layout */
    padding-top: 0px !important;
}

.form-content {
    order: 1;
    flex: 0 0 400px !important; /* Fixed width for form column */
    max-width: 400px !important;
    min-width: 350px !important;
    margin-top: 0px !important;
    padding-top: 0px !important;
}

.tab {
    order: 2;
    flex: 0 0 700px !important; /* Fixed width for consistent accordion */
    max-width: 700px !important;
    min-width: 700px !important;
    width: 700px !important;
    float: none;
    margin-top: 0px !important;
    padding-top: 0px !important;
}


/* 4. LEFT COLUMN: FORM STYLES
----------------------------------------------------------------------*/
/* Hides ALL old form labels */
.span-form-group-orgid,
.span-form-group-projids,
.span-form-group {
    display: none !important;
}

/* Styles ALL text input fields (GCP and AWS) */
.form-group-class-orgid,
.form-group-class-projids,
.form-group-class {
    width: 100% !important;
    padding: 12px 15px;
    margin-bottom: 15px !important; /* Consistent spacing */
    margin-top: 0px !important;
    border: 1px solid #ced4da;
    border-radius: 6px !important;
    font-size: 14px;
    box-sizing: border-box;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    margin-left: 0 !important; /* Reset margins */
    margin-right: 0 !important;
}

/* Adds modern focus effect to ALL inputs */
.form-group-class-orgid:focus,
.form-group-class-projids:focus,
.form-group-class:focus {
    outline: none;
    border-color: #0d6efd;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

/* Styles the form group container */
.form-group {
    padding: 0 !important;
    margin: 0 !important;
}

/* Modern File Input Styling */
input[type="file"] {
    width: 100% !important;
    padding: 0 !important;
    margin-bottom: 15px !important; /* Consistent spacing */
    margin-top: 0px !important;
    border: 2px dashed #ced4da !important;
    border-radius: 8px !important;
    background-color: #f8f9fa !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
    height: 50px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

input[type="file"]:hover {
    border-color: #0d6efd !important;
    background-color: #e7f1ff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15) !important;
}

input[type="file"]:focus {
    outline: none !important;
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25) !important;
}

/* Custom file input button styling */
input[type="file"]::file-selector-button {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%) !important;
    color: white !important;
    border: none !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    margin-right: 12px !important;
    transition: all 0.2s ease !important;
}

input[type="file"]::file-selector-button:hover {
    background: linear-gradient(135deg, #0b5ed7 0%, #0a58ca 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.3) !important;
}

/* Webkit browsers (Chrome, Safari, Edge) */
input[type="file"]::-webkit-file-upload-button {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%) !important;
    color: white !important;
    border: none !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    margin-right: 12px !important;
    transition: all 0.2s ease !important;
}

input[type="file"]::-webkit-file-upload-button:hover {
    background: linear-gradient(135deg, #0b5ed7 0%, #0a58ca 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.3) !important;
}

/* Styles the AI Summary switch container */
 .slider-container {
    display: flex;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 15px;
    margin-left: 0 !important; /* Reset margin */
    color: #495057;
}

/* Modern styling for the main "Generate" button */
 .arrow-btn.tilt-btn {
    width: 100%;
    padding: 14px 20px;
    background-color: #0d6efd !important;
    color: white;
    font-size: 16px;
    font-weight: 500;
    border: none;
    border-radius: 6px !important;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin: 15px 0 0 0 !important; /* Consistent spacing with form fields */
}

.arrow-btn.tilt-btn:hover:not(:disabled) {
    background-color: #0b5ed7 !important;
}


/* 5. RIGHT COLUMN: ACCORDION STYLES (Highly specific to override old rules)
----------------------------------------------------------------------*/
 .tab button.tablinks {
    width: 100% !important;
    padding: 16px 20px !important; /* Consistent padding */
    margin-bottom: 12px !important; /* Spacing between items */
    background-color: #ffffff !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 8px !important;
    text-align: left !important;
    font-size: 15px !important;
    font-weight: 500 !important;
    color: #212529 !important;
    position: relative;
    transition: background-color 0.2s ease, border-color 0.2s ease !important;
    display: block !important;
    box-sizing: border-box !important;
    min-width: 700px !important; /* Fixed width for consistency */
    max-width: 700px !important;
    transform: none !important; /* No transform effects */
    cursor: pointer !important;
}

.tab button.tablinks:hover {
    background-color: #f8f9fa !important; /* Light hover background */
    border-color: #dee2e6 !important;
    transform: none !important; /* No size change on hover */
    padding: 16px 20px !important; /* Same padding as default */
    width: 100% !important; /* Same width as default */
}

.tab button.tablinks.active {
    background-color: #f0f2f5 !important; /* Active background */
    border-color: #dee2e6 !important;
    color: #212529 !important;
    padding: 16px 20px !important; /* Same padding as default */
    transform: none !important; /* No size change when active */
    width: 100% !important; /* Same width as default */
}

.tab button.tablinks.active::after {
    content: '▴' !important; /* Up arrow */
    color: #212529 !important; /* Same color as text */
}

.tab button.tablinks::after {
    content: '▾' !important;
    font-size: 18px !important;
    font-weight: normal !important;
    color: #6c757d !important;
    float: right !important;
    margin-top: -2px;
}

 .tab .tabcontent {
    display: none;
    float: none !important;
    width: 100% !important;
    min-width: 700px !important; /* Fixed width for consistency */
    max-width: 700px !important;
    border: 1px solid #dee2e6 !important;
    border-top: none !important;
    margin-top: -11px !important;
    margin-bottom: 12px !important; /* Match button spacing */
    padding: 25px !important; /* Increased padding for better readability */
    border-radius: 0 0 8px 8px !important; /* Match button radius */
    background-color: #ffffff !important;
    box-sizing: border-box !important;
    overflow: hidden !important; /* Prevent content from breaking layout boundaries */
    min-height: 200px !important; /* Minimum height for video content */
    height: auto !important;
    line-height: 1.6 !important; /* Better line spacing for readability */
    position: relative !important; /* Ensure proper positioning */
    z-index: 1 !important; /* Ensure content stays within bounds */
}

/* Ensure content inside accordion doesn't break layout */
.tab .tabcontent iframe,
.tab .tabcontent img {
    max-width: 100% !important;
    height: auto !important;
}

.tab .tabcontent iframe {
    width: 100% !important;
    height: 450px !important; /* Fixed height for better video viewing */
    min-height: 350px !important; /* Minimum height for video content */
    border: none !important;
    border-radius: 4px !important;
    margin: 10px 0 !important; /* Add margin around video */
}

/* Video element styling */
.tab .tabcontent video {
    width: 100% !important;
    height: auto !important;
    min-height: 300px !important; /* Minimum height for video content */
    max-height: 500px !important; /* Increased max height */
    border-radius: 4px !important;
    margin: 10px 0 !important; /* Add margin around video */
}

/* Card body styling for form container */
.form-content .card-body {
    padding: 0 !important;
    border: none !important;
    background: transparent !important;
}


/* =================================================================== */
/* == UI REFINEMENTS & SPACING ADJUSTMENTS                         == */
/* =================================================================== */

/* 1. NEW UI FOR PRIMARY NAVIGATION (ASSESSMENT, MIGRATION, ETC.)
----------------------------------------------------------------------*/
/* NOTE: You may need to adjust these class names (.main-nav-container,
   .main-nav-link) to match the actual classes in your HTML header. */

/* Styles the container holding the main navigation buttons */
.main-nav-container {
    display: flex;
    border: 1px solid #dee2e6; /* Light grey border around the group */
    border-radius: 6px;
    overflow: hidden; /* Keeps the rounded corners neat */
}

/* Styles each individual navigation link */
.main-nav-link {
    padding: 10px 20px;
    color: #495057; /* Dark grey text */
    text-decoration: none;
    font-weight: 500;
    border-right: 1px solid #dee2e6; /* Vertical line separator */
    transition: background-color 0.2s ease;
}

/* Removes the vertical line from the very last button */
.main-nav-container .main-nav-link:last-child {
    border-right: none;
}

/* Style for the currently ACTIVE or selected link (e.g., 'Inventory') */
.main-nav-container .main-nav-link.active {
    background-color: #0d6efd; /* Primary Blue */
    color: white;
    /* padding: 10px 100px; */
}

/* Adds a hover effect for all non-active links */
.main-nav-container .main-nav-link:not(.active):hover {
    background-color: #f8f9fa; /* Light grey on hover */
}


/* 2. REDUCE HORIZONTAL SPACE IN SECONDARY TABS (GCP, AWS, ETC.)
----------------------------------------------------------------------*/
/* This reduces the left and right padding on the cloud provider tabs,
   bringing them closer together. */
.what-tab .nav.nav-tabs {
    display: flex !important;
    justify-content: center !important; /* Center all tabs */
    width: 100% !important;
    margin: 0 auto !important;
}

.what-tab .nav.nav-tabs .nav-item .nav-link {
    padding: 12px 25px !important; /* Consistent padding for all states */
    font-size: 15px !important;
    transition: background-color 0.2s ease !important;
    min-width: 120px !important; /* Minimum width for consistency */
    text-align: center !important;
    margin: 0 2px !important; /* Small gap between tabs */
    border-radius: 6px 6px 0 0 !important; /* Rounded top corners */
}
.what-tab .nav.nav-tabs .nav-item .nav-link.active {
    padding: 12px 25px !important; /* Same padding as inactive */
    background-color: #1a73e8 !important; /* Blue background for active */
    color: #ffffff !important; /* White text for active */
    transform: none !important; /* Remove any transform effects */
    box-shadow: none !important; /* Remove any shadow effects */
    min-width: 120px !important; /* Same width as inactive */
    text-align: center !important;
    margin: 0 2px !important; /* Same margin as inactive */
}
.what-tab .nav.nav-tabs .nav-item .nav-link:hover:not(.active) {
    background-color: #f0f2f5 !important;
}

/* Override any inherited styles that might cause issues */
.tab button.tablinks,
.tab button.tablinks.active,
.tab button.tablinks:hover,
.tab button.tablinks:focus {
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
    max-width: none !important;
}

/* Ensure tab content paragraph has proper spacing */
.tab .tabcontent p {
    margin-bottom: 10px !important;
    line-height: 1.6 !important;
}

/* Remove any animation or transition effects that cause shift */
.tab button.tablinks * {
    transition: none !important;
    transform: none !important;
}

/* Reduce overall section height to show more content */
.what-we-do {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    min-height: auto !important;
    margin-top: 0px !important;
}

.what-we-do .what-tab .tab-content {
    min-height: auto !important;
    height: auto !important;
}

/* Ensure tab-pane has proper height */
.tab-pane {
    min-height: auto !important;
    height: auto !important;
}

/* FULL WIDTH LAYOUT - Override Bootstrap container */
.container-fluid {
    max-width: 100% !important;
    width: 100% !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.what-tab .container-fluid {
    max-width: 100% !important;
    width: 100% !important;
    padding: 0 !important;
}

/* Remove gap between nav-container and content */
.nav-container {
    margin-bottom: 0px !important;
    padding-bottom: 0px !important;
    max-width: 100% !important;
    width: 100% !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
}

.container-fluid.p-0 {
    padding: 0 !important;
    margin-bottom: 0px !important;
    max-width: 100% !important;
}

/* Ensure nav-tabs background extends properly */
.nav-container .nav-tabs {
    margin-bottom: 0px !important;
    padding-bottom: 0px !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* Join the tab content with nav section */
.tab-pane.fade {
    margin-top: 0px !important;
    padding-top: 0px !important;
}

/* Ensure section uses full width */
.section-margin,
.what-we-do {
    max-width: 100% !important;
    width: 100% !important;
}

/* Override any Bootstrap container max-width */
.container,
.container-lg,
.container-md,
.container-sm,
.container-xl {
    max-width: 100% !important;
}

/* Ensure nav-tabs are visible and properly styled */
.nav-container .nav-tabs {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    background-color: #f0f2f5 !important;
    border-bottom: none !important;
    padding: 0 60px !important; /* Match content padding */
    margin: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    justify-content: center !important;
    flex-wrap: nowrap !important;
}

.nav-container .nav-tabs li {
    display: inline-block !important;
    visibility: visible !important;
    list-style: none !important;
    margin: 0 !important;
}

.nav-container .nav-tabs li a {
    display: block !important;
    visibility: visible !important;
    padding: 14px 25px !important; /* Consistent padding */
    margin: 0 2px !important; /* Small gap between tabs */
    color: #5f6368 !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    text-decoration: none !important;
    border: none !important;
    background-color: transparent !important;
    transition: background-color 0.2s ease !important;
    white-space: nowrap !important;
    min-width: 100px !important; /* Minimum width for consistency */
    text-align: center !important;
    border-radius: 6px 6px 0 0 !important; /* Rounded top corners */
}

.nav-container .nav-tabs li a.active {
    background-color: #1a73e8 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    padding: 14px 25px !important; /* Same padding as inactive */
    margin: 0 2px !important; /* Same margin as inactive */
    min-width: 100px !important; /* Same width as inactive */
    text-align: center !important;
    border-radius: 6px 6px 0 0 !important;
}

.nav-container .nav-tabs li a:hover:not(.active) {
    background-color: #e8eaed !important;
}
